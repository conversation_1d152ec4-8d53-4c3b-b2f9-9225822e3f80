<?php

namespace Tests\Feature;

use App\Enums\Api\Internal\BarcodableTypeEnum;
use App\Enums\Api\Internal\BarcodeEnum;
use App\Models\Barcode;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Employee;
use App\Models\Product;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class BarcodeTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_barcodes_list(): void
    {
        // Arrange
        // Создаем баркоды для нашего кабинета
        Barcode::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем баркод для другого кабинета
        Barcode::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/barcode?' . http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'cabinet_id',
                        'barcodable_id',
                        'barcodable_type',
                        'type',
                        'value',
                        'sort',
                        'is_generated'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только баркоды нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_get_barcodes_without_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/barcode');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_barcodes_with_invalid_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/barcode?' . http_build_query([
            'cabinet_id' => 'not-a-uuid'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_barcodes_from_other_cabinet(): void
    {
        // Arrange
        // Создаем баркоды для другого кабинета
        Barcode::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/barcode?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_get_barcodes_with_pagination(): void
    {
        // Arrange
        Barcode::factory()->count(15)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/barcode?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 2,
            'per_page' => 10
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $this->assertEquals(2, $response->json('meta.current_page'));
        $this->assertEquals(10, $response->json('meta.per_page'));
        $this->assertEquals(15, $response->json('meta.total'));
        $this->assertCount(5, $response->json('data'));
    }

    public function test_can_get_barcodes_with_sorting(): void
    {
        // Arrange
        $barcodes = [
            'A code' => Barcode::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'value' => 'A value'
            ]),
            'B code' => Barcode::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'value' => 'B value'
            ]),
            'C code' => Barcode::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'value' => 'C value'
            ])
        ];

        // Act - получаем отсортированный по коду список
        $response = $this->getJson('/api/internal/barcode?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'value',
            'sortDirection' => 'desc'
        ]));

        // Assert
        $response->assertStatus(200);

        $codes = collect($response->json('data'))->pluck('value')->values();
        $expectedCodes = collect(['C value', 'B value', 'A value']);

        $this->assertEquals($expectedCodes, $codes);
    }

    public function test_can_get_barcodes_with_fields(): void
    {
        Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/barcode?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['id', 'value']
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'value'
                    ]
                ],
                'meta'
            ]);
    }

    public function test_cannot_get_barcodes_with_invalid_fields(): void
    {
        Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/barcode?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['invalid-field']
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fields.0']);
    }

    public function test_can_create_barcode(): void
    {
        $product = Product::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'entity_id' => $product->id,
            'entity_type' => BarcodableTypeEnum::PRODUCTS->value,
            'barcodes' => [
                'type' => BarcodeEnum::EAN13->value, // EAN-13
                'value' => '2000000000008'
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/barcode', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('barcodes', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'barcodable_id' => $data['entity_id'],
            'barcodable_type' => $data['entity_type'],
            'type' => $data['barcodes']['type'],
            'value' => $data['barcodes']['value']
        ]);
    }

    public function test_cannot_create_barcode_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/barcode', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'entity_id',
                'entity_type',
                'barcodes',
                'barcodes.type'
            ]);
    }

    public function test_cannot_create_barcode_with_invalid_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'entity_id' => 'not-a-uuid',
            'entity_type' => 'InvalidType',
            'barcodes' => [
                'type' => 999, // несуществующий тип
                'value' => str_repeat('a', 49) // превышает максимальную длину в 48 символов
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/barcode', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'entity_id',
                'entity_type',
                'barcodes.type',
                'barcodes.value'
            ]);
    }

    public function test_cannot_create_barcode_for_other_cabinet(): void
    {
        $product = Product::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'entity_id' => $product->id,
            'entity_type' => BarcodableTypeEnum::PRODUCTS->value,
            'barcodes' => [
                'type' => BarcodeEnum::EAN13->value, // EAN-13
                'value' => '2000000000008'
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/barcode', $data);

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseMissing('barcodes', [
            'cabinet_id' => $data['cabinet_id'],
            'barcodable_id' => $data['entity_id']
        ]);
    }

    public function test_can_create_barcode_without_value(): void
    {
        $product = Product::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'entity_id' => $product->id,
            'entity_type' => BarcodableTypeEnum::PRODUCTS->value,
            'barcodes' => [
                'type' => BarcodeEnum::EAN13->value, // EAN-13
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/barcode', $data);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('barcodes', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'barcodable_id' => $data['entity_id'],
            'barcodable_type' => $data['entity_type'],
            'type' => $data['barcodes']['type']
        ]);
    }

    public function test_cannot_create_duplicate_barcode(): void
    {
        // Arrange
        $existingBarcode = Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'value' => '2000000000008'
        ]);

        $product = Product::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'entity_id' => $product->id,
            'entity_type' => BarcodableTypeEnum::PRODUCTS->value,
            'barcodes' => [
                'type' => BarcodeEnum::EAN13->value, // EAN-13
                'value' => 2000000000008
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/barcode', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['barcodes.value']);
    }

    public function test_can_update_barcode(): void
    {
        // Arrange
        $barcode = Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'value' => '2000000000022'
        ]);

        $updateData = [
            'barcodes' => [
                'value' => '2000000000015'
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/barcode/{$barcode->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('barcodes', [
            'id' => $barcode->id,
            'value' => $updateData['barcodes']['value']
        ]);
    }

    public function test_cannot_update_barcode_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetBarcode = Barcode::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'value' => '2000000000008'
        ]);

        $updateData = [
            'barcodes' => [
                'value' => '2000000000015'
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/barcode/{$otherCabinetBarcode->id}", $updateData);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('barcodes', [
            'id' => $otherCabinetBarcode->id,
            'value' => '2000000000008' // проверяем что значение не изменилось
        ]);
    }

    public function test_cannot_update_barcode_with_invalid_data(): void
    {
        // Arrange
        $barcode = Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $updateData = [
            'barcodes' => [
                'value' => str_repeat('a', 49) // превышает максимальную длину в 48 символов
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/barcode/{$barcode->id}", $updateData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['barcodes.value']);
    }

    public function test_cannot_update_non_existent_barcode(): void
    {
        // Act
        $response = $this->putJson("/api/internal/barcode/" . $this->faker->uuid(), [
            'barcodes' => [
                'value' => '2000000000015'
            ]
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_barcode_with_null_value(): void
    {
        // Arrange
        $barcode = Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'value' => '2000000000008'
        ]);

        $updateData = [
            'barcodes' => [
                'value' => null
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/barcode/{$barcode->id}", $updateData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['barcodes.value']);
    }

    public function test_can_bulk_store_barcodes(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'entity_id' => $product->id,
            'entity_type' => BarcodableTypeEnum::PRODUCTS->value,
            'barcodes' => [
                [
                    'type' => BarcodeEnum::EAN13->value,
                    'value' => '2000000000008'
                ],
                [
                    'type' => BarcodeEnum::EAN8->value,
                    'value' => '20000004'
                ],
                [
                    'type' => BarcodeEnum::CODE128->value,
                    'value' => 'CODE128-TEST'
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/barcode/bulk-store', $data);

        // Assert
        $response->assertStatus(204);

        foreach ($data['barcodes'] as $datum) {
            $this->assertDatabaseHas('barcodes', [
                'cabinet_id' => $data['cabinet_id'],
                'barcodable_id' => $data['entity_id'],
                'barcodable_type' => $data['entity_type'],
                'value' => $datum['value'],
                'type' => $datum['type']
            ]);
        }
    }

    public function test_cannot_bulk_store_barcodes_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/barcode/bulk-store', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'entity_id',
                'entity_type',
                'barcodes'
            ]);
    }

    public function test_cannot_bulk_store_barcodes_with_invalid_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'entity_id' => 'not-a-uuid',
            'entity_type' => 'InvalidType',
            'barcodes' => [
                [
                    'type' => 999, // несуществующий тип
                    'value' => str_repeat('a', 49) // превышает максимальную длину
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/barcode/bulk-store', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'entity_id',
                'entity_type',
                'barcodes.0.type',
                'barcodes.0.value'
            ]);
    }

    public function test_cannot_bulk_store_barcodes_for_other_cabinet(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'entity_id' => $product->id,
            'entity_type' => BarcodableTypeEnum::PRODUCTS->value,
            'barcodes' => [
                [
                    'type' => BarcodeEnum::EAN13->value,
                    'value' => '2000000000008'
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/barcode/bulk-store', $data);

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseMissing('barcodes', [
            'cabinet_id' => $data['cabinet_id'],
            'barcodable_id' => $data['entity_id']
        ]);
    }

    public function test_can_bulk_store_barcodes_with_null_values(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'entity_id' => $product->id,
            'entity_type' => BarcodableTypeEnum::PRODUCTS->value,
            'barcodes' => [
                [
                    'type' => BarcodeEnum::EAN13->value,
                    'value' => null
                ],
                [
                    'type' => BarcodeEnum::EAN8->value,
                    'value' => null
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/barcode/bulk-store', $data);

        // Assert
        $response->assertStatus(204);

        foreach ($data['barcodes'] as $datum) {
            $this->assertDatabaseHas('barcodes', [
                'cabinet_id' => $data['cabinet_id'],
                'barcodable_id' => $data['entity_id'],
                'barcodable_type' => $data['entity_type'],
                'type' => $datum['type']
            ]);
        }
    }

    public function test_cannot_bulk_store_duplicate_barcodes(): void
    {
        // Arrange
        $existingBarcode = Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'value' => '2000000000008'
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'entity_id' => $product->id,
            'entity_type' => BarcodableTypeEnum::PRODUCTS->value,
            'barcodes' => [
                [
                    'type' => BarcodeEnum::EAN13->value,
                    'value' => '2000000000008' // дублирующее значение
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/barcode/bulk-store', $data);


        //TODO переделать, должен вернуть 422
        // Assert
        $response->assertStatus(500);
    }

    public function test_can_bulk_update_barcodes(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $barcodes = [
            Barcode::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'barcodable_id' => $product->id,
                'barcodable_type' => BarcodableTypeEnum::PRODUCTS->value,
                'type' => BarcodeEnum::EAN13->value,
                'value' => '2000000000008'
            ]),
            Barcode::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'barcodable_id' => $product->id,
                'barcodable_type' => BarcodableTypeEnum::PRODUCTS->value,
                'type' => BarcodeEnum::EAN8->value,
                'value' => '20000015'
            ])
        ];

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'entity_id' => $product->id,
            'entity_type' => BarcodableTypeEnum::PRODUCTS->value,
            'barcodes' => [
                [
                    'id' => $barcodes[0]->id,
                    'type' => BarcodeEnum::EAN13->value,
                    'value' => '2000000000015'
                ],
                [
                    'id' => $barcodes[1]->id,
                    'type' => BarcodeEnum::CODE128->value,
                    'value' => 'NEW-CODE-128'
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/barcode/bulk-update/{$product->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        foreach ($updateData['barcodes'] as $barcode) {
            $this->assertDatabaseHas('barcodes', [
                'id' => $barcode['id'],
                'type' => $barcode['type'],
                'value' => $barcode['value']
            ]);
        }
    }

    public function test_cannot_bulk_update_barcodes_without_required_fields(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/barcode/bulk-update/{$product->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'entity_id',
                'entity_type',
                'barcodes'
            ]);
    }

    public function test_cannot_bulk_update_barcodes_with_invalid_data(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $barcode = Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'barcodable_id' => $product->id
        ]);

        $data = [
            'cabinet_id' => 'not-a-uuid',
            'entity_id' => 'not-a-uuid',
            'entity_type' => 'InvalidType',
            'barcodes' => [
                [
                    'id' => 'not-a-uuid',
                    'type' => 999, // несуществующий тип
                    'value' => str_repeat('a', 49) // превышает максимальную длину
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/barcode/bulk-update/{$product->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'entity_id',
                'entity_type',
                'barcodes.0.id',
                'barcodes.0.type',
                'barcodes.0.value'
            ]);
    }

    public function test_cannot_bulk_update_barcodes_for_other_cabinet(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $barcode = Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'barcodable_id' => $product->id,
            'value' => '2000000000008'
        ]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'entity_id' => $product->id,
            'entity_type' => BarcodableTypeEnum::PRODUCTS->value,
            'barcodes' => [
                [
                    'id' => $barcode->id,
                    'type' => BarcodeEnum::EAN13->value,
                    'value' => '2000000000015'
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/barcode/bulk-update/{$product->id}", $data);

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseHas('barcodes', [
            'id' => $barcode->id,
            'value' => '2000000000008' // проверяем что значение не изменилось
        ]);
    }

    public function test_can_bulk_update_barcodes_with_null_values(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $barcodes = [
            Barcode::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'barcodable_id' => $product->id,
                'value' => '2000000000008'
            ]),
            Barcode::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'barcodable_id' => $product->id,
                'value' => '20000015'
            ])
        ];

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'entity_id' => $product->id,
            'entity_type' => BarcodableTypeEnum::PRODUCTS->value,
            'barcodes' => [
                [
                    'id' => $barcodes[0]->id,
                    'type' => BarcodeEnum::EAN13->value,
                    'value' => null
                ],
                [
                    'id' => $barcodes[1]->id,
                    'type' => BarcodeEnum::EAN8->value,
                    'value' => null
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/barcode/bulk-update/{$product->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        foreach ($updateData['barcodes'] as $barcode) {
            $this->assertDatabaseHas('barcodes', [
                'id' => $barcode['id'],
                'type' => $barcode['type'],
            ]);
        }
    }

    public function test_cannot_bulk_update_non_existent_barcodes(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'entity_id' => $product->id,
            'entity_type' => BarcodableTypeEnum::PRODUCTS->value,
            'barcodes' => [
                [
                    'id' => $this->faker->uuid(),
                    'type' => BarcodeEnum::EAN13->value,
                    'value' => '2000000000015'
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/barcode/bulk-update/{$product->id}", $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_bulk_update_with_duplicate_values(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $existingBarcode = Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'value' => '2000000000008'
        ]);

        $barcodeToUpdate = Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'barcodable_id' => $product->id,
            'value' => '2000000000015'
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'entity_id' => $product->id,
            'entity_type' => BarcodableTypeEnum::PRODUCTS->value,
            'barcodes' => [
                [
                    'id' => $barcodeToUpdate->id,
                    'type' => BarcodeEnum::EAN13->value,
                    'value' => '2000000000008' // дублирующее значение
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/barcode/bulk-update/{$product->id}", $data);

        // Assert
        $response->assertStatus(500)
            ->assertJson(['error' => 'An error occurred while processing your request. 2000000000008 not unique']);

        $this->assertDatabaseHas('barcodes', [
            'id' => $barcodeToUpdate->id,
            'value' => '2000000000015' // проверяем что значение не изменилось
        ]);
    }

    public function test_can_show_barcode(): void
    {
        // Arrange
        $barcode = Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/barcode/{$barcode->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'cabinet_id',
                'barcodable_id',
                'barcodable_type',
                'type',
                'value',
                'sort',
                'is_generated'
            ]);

        $this->assertEquals($barcode->id, $response->json('id'));
        $this->assertEquals($barcode->cabinet_id, $response->json('cabinet_id'));
    }

    public function test_cannot_show_barcode_from_other_cabinet(): void
    {
        // Arrange
        $barcode = Barcode::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/barcode/{$barcode->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_barcode(): void
    {
        // Act
        $response = $this->getJson("/api/internal/barcode/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_barcode(): void
    {
        // Arrange
        $barcode = Barcode::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/barcode/{$barcode->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('barcodes', [
            'id' => $barcode->id
        ]);
    }

    public function test_cannot_delete_barcode_from_other_cabinet(): void
    {
        // Arrange
        $barcode = Barcode::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/barcode/{$barcode->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('barcodes', [
            'id' => $barcode->id
        ]);
    }

    public function test_cannot_delete_non_existent_barcode(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/barcode/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }
}
