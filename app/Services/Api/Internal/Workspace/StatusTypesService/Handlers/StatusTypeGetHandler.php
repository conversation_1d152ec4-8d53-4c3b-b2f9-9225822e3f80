<?php

namespace App\Services\Api\Internal\Workspace\StatusTypesService\Handlers;

use App\Contracts\Repositories\StatusTypesRepositoryContract;
use Illuminate\Support\Collection;

readonly class StatusTypeGetHandler
{
    public function __construct(
        private StatusTypesRepositoryContract $repository
    ) {
    }
    public function run(): Collection
    {
        return $this->repository->get();
    }
}
