<?php

namespace App\Services\Api\Internal\Procurement\AcceptanceItemsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\AcceptanceItemsRepositoryContract;
use App\Contracts\Repositories\AcceptanceRepositoryContract;
use App\Contracts\Repositories\VatRatesRepositoryContract;

use App\Jobs\FIFOJobs\RecalculationAfterUpdateAcceptanceItemJob;
use App\Services\Api\Internal\Procurement\Acceptances\Traits\AcceptanceTotalCalculator;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\DTO\AcceptanceItemDto;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Bus;
use Symfony\Component\Process\Exception\InvalidArgumentException;

class AcceptanceItemsUpdateHandler
{
    use HasOrderedUuid;
    use AcceptanceTotalCalculator;

    private HasUpdateArrayDtoContract $dto;

    public function __construct(
        private readonly AcceptanceItemsRepositoryContract $acceptanceItemsRepository,
        private readonly AcceptanceRepositoryContract $acceptanceRepository,
        private readonly VatRatesRepositoryContract $vatRatesRepository
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof AcceptanceItemDto) {
            throw new InvalidArgumentException('Unsupported DTO type');
        }
        $this->dto = $dto;

        $item = $this->acceptanceItemsRepository->show($this->dto->resourceId);

        if (!$item) {
            throw new \RuntimeException('Item not found in handler');
        }

        $acceptance = $this->acceptanceRepository->show($item->acceptance_id);

        if (!$acceptance) {
            throw new \RuntimeException('Acceptance not found in handler');
        }

        // Если has_vat = false и vat_rate_id не указан, автоматически выбираем ставку "Без НДС"
        if ($acceptance->has_vat === false && $this->dto->vat_rate_id === null) {
            $noVatRate = $this->vatRatesRepository->getByRateAndCabinet(0, $acceptance->cabinet_id);
            if ($noVatRate) {
                $this->dto->vat_rate_id = $noVatRate->id;
            }
        }

        $this->acceptanceItemsRepository->update(
            $this->dto->resourceId,
            $this->dto->toUpdateArray(),
        );
        
        // Пересчитываем общую сумму приемки с учетом НДС
        $this->updateAcceptanceTotal($acceptance->id);

        Bus::dispatch(new RecalculationAfterUpdateAcceptanceItemJob($item, $this->dto));
    }
}
