<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Jobs\FIFOJobs\HandleFifoJob;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\DTO\ShipmentItemDTO;
use App\Traits\HasOrderedUuid;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use InvalidArgumentException;
use RuntimeException;

class ShipmentItemsCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly ShipmentItemsRepositoryContract $shipmentItemsRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    /**
     * @throws Exception
     */
    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof ShipmentItemDTO) {
            throw new InvalidArgumentException('Invalid dto');
        }

        $clone = $this->shipmentItemsRepository
            ->checkItemExists($dto->productId, $dto->shipmentId);

        if ($clone) {
            throw new RuntimeException('Item already exists');
        }
        $this->shipmentItemsRepository->insert(
            $dto->toInsertArray($this->resourceId)
        );

        DB::table('shipments')
            ->where('id', $dto->shipmentId)
            ->update([
                'total_price' => DB::raw("CAST(COALESCE(total_price, '0') AS NUMERIC) + " . $dto->price)
            ]);


        Queue::push(new HandleFifoJob($this->resourceId));

        return $this->resourceId;
    }
}
