<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasDeliveryArrayContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Carbon;

class ShipmentDTO implements DtoContract, HasInsertArrayDtoContract, HasDeliveryArrayContract, HasUpdateArrayDtoContract
{
    use HasOrderedUuid;

    public function __construct(
        public ?string $cabinetId,
        public ?string $number,
        public ?string $dateFrom,
        public ?string $statusId,
        public string $legalEntityId,
        public string $contractorId,
        public string $warehouseId,
        public ?string $salesChannelId,
        public string $currencyId,
        public string $currencyValue,
        public ?string $consigneeId,
        public ?string $transporterId,
        public ?string $cargoName,
        public ?string $shipperInstructions,
        public ?string $venicle,
        public ?string $venicleNumber,
        public ?int $totalSeats,
        public ?string $govermentContractId,
        public ?string $comment,
        public int $userId,
        public ?string $resourceId,
        public string $employeeId,
        public string $departmentId,
        public bool $priceIncludesVat = true,
        public string $overheadCost = '0',
        public bool $held = true,
        public array $files = [],
        public array $deliveryInfo = [],
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'employee_id' => $this->employeeId,
            'department_id' => $this->departmentId,
            'number' => $this->number,
            'date_from' => $this->dateFrom,
            'status_id' => $this->statusId,
            'held' => $this->held,
            'legal_entity_id' => $this->legalEntityId,
            'contractor_id' => $this->contractorId,
            'warehouse_id' => $this->warehouseId,
            'sales_channel_id' => $this->salesChannelId,
            'currency_id' => $this->currencyId,
            'currency_value' => $this->currencyValue,
            'consignee_id' => $this->consigneeId,
            'transporter_id' => $this->transporterId,
            'cargo_name' => $this->cargoName,
            'shipper_instructions' => $this->shipperInstructions,
            'venicle' => $this->venicle,
            'venicle_number' => $this->venicleNumber,
            'total_seats' => $this->totalSeats,
            'goverment_contract_id' => $this->govermentContractId,
            'comment' => $this->comment,
            'price_includes_vat' => $this->priceIncludesVat,
            'overhead_cost' => $this->overheadCost,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'employee_id' => $this->employeeId,
            'department_id' => $this->departmentId,
            'number' => $this->number,
            'date_from' => $this->dateFrom,
            'status_id' => $this->statusId,
            'held' => $this->held,
            'legal_entity_id' => $this->legalEntityId,
            'contractor_id' => $this->contractorId,
            'warehouse_id' => $this->warehouseId,
            'sales_channel_id' => $this->salesChannelId,
            'currency_id' => $this->currencyId,
            'currency_value' => $this->currencyValue,
            'consignee_id' => $this->consigneeId,
            'transporter_id' => $this->transporterId,
            'cargo_name' => $this->cargoName,
            'shipper_instructions' => $this->shipperInstructions,
            'venicle' => $this->venicle,
            'venicle_number' => $this->venicleNumber,
            'total_seats' => $this->totalSeats,
            'goverment_contract_id' => $this->govermentContractId,
            'comment' => $this->comment,
            'price_includes_vat' => $this->priceIncludesVat,
            'overhead_cost' => $this->overheadCost,
        ];
    }

    public function toDeliveryArray(string $id): array
    {
        return [
            'id' => $this->generateUuid(),
            'created_at' => Carbon::now(),
            'shipment_id' => $id,
            'comment' => $this->deliveryInfo['comment'] ?? null,
            'post_code' => $this->deliveryInfo['post_code'] ?? null,
            'country' => $this->deliveryInfo['country'] ?? null,
            'region' => $this->deliveryInfo['region'] ?? null,
            'city' => $this->deliveryInfo['city'] ?? null,
            'street' => $this->deliveryInfo['street'] ?? null,
            'house' => $this->deliveryInfo['house'] ?? null,
            'office' => $this->deliveryInfo['office'] ?? null,
            'other' => $this->deliveryInfo['other'] ?? null
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            number: $data['number'] ?? null,
            dateFrom: isset($data['date_from']) ? Carbon::parse($data['date_from']) : Carbon::now(),
            statusId: $data['status_id'] ?? null,
            legalEntityId: $data['legal_entity_id'] ?? null,
            contractorId: $data['contractor_id'] ?? null,
            warehouseId: $data['warehouse_id'] ?? null,
            salesChannelId: $data['sales_channel_id'] ?? null,
            currencyId: $data['currency_id'],
            currencyValue: $data['currency_value'] ?? 1,
            consigneeId: $data['consignee_id'] ?? null,
            transporterId: $data['transporter_id'] ?? null,
            cargoName: $data['cargo_name'] ?? null,
            shipperInstructions: $data['shipper_instructions'] ?? null,
            venicle: $data['venicle'] ?? null,
            venicleNumber: $data['venicle_number'] ?? null,
            totalSeats: $data['total_seats'] ?? null,
            govermentContractId: $data['goverment_contract_id'] ?? null,
            comment: $data['comment'] ?? null,
            userId: auth()->user()->id,
            resourceId: $data['id'] ?? null,
            employeeId: $data['employee_id'],
            departmentId: $data['department_id'],
            priceIncludesVat: $data['price_includes_vat'] ?? true,
            overheadCost: $data['overhead_cost'] ?? 0,
            held: $data['held'] ?? true,
            files: $data['files'] ?? [],
            deliveryInfo: $data['delivery_info'] ?? []
        );
    }
}
