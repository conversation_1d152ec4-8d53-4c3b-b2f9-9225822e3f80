<?php

namespace App\Modules\Marketplaces\Services\Traits;

use Illuminate\Support\Facades\DB;

trait HasStatuses
{
    protected function preloadStatuses(string $name): void
    {
        $statusType = DB::table('status_types')
            ->where('name', $name)
            ->first();

        if ($statusType) {
            $statuses = DB::table('statuses')
                ->where('cabinet_id', $this->cabinetId)
                ->where('type_id', $statusType->id)
                ->get();

            foreach ($statuses as $status) {
                $this->statusesCache[$status->name] = $status;
            }

            $this->statusesCache['_type'] = $statusType;
        }
    }
}
