<?php

namespace App\Entities;

class ContractorGroupsEntity extends BaseEntity
{
    public static string $table = 'contractor_groups';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'cabinet_id',
        'name',
        'employee_id',
        'department_id',
    ];

    public function employees(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function departments(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }
}
