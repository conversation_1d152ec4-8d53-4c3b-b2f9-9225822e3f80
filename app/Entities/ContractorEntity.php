<?php

namespace App\Entities;

class ContractorEntity extends BaseEntity
{
    public static string $table = 'contractors';

    public static array $fields = [
        'id',
        'title',
        'created_at',
        'updated_at',
        'deleted_at',
        'archived_at',
        'cabinet_id',
        'shared_access',
        'employee_id',
        'department_id',
        'title',
        'status_id',
        'is_buyer',
        'is_supplier',
        'phone',
        'fax',
        'email',
        'description',
        'code',
        'external_code',
        'discounts_and_prices',
        'discount_card_number',
        'is_default'
    ];

    public function accounts(): RelationBuilder
    {
        return $this->hasMany(ContractorAccountEntity::class, 'id', 'contractor_id')
            ->fields(['id', 'bank'])
            ->filter(['is_main' => true]);
    }

    public function group(): RelationBuilder
    {
        return $this->hasMany(ContractorGroupEntity::class, 'id', 'contractor_id')
        ->fields(['contractor_id', 'group_id']);
    }

    public function groups(): RelationBuilder
    {
        return $this->manyToMany(
            ContractorGroupsEntity::class,
            'contractor_id',               // Foreign key in the pivot table for ContractorEntity
            'group_id',         // Foreign key in the pivot table for ContractorGroupsEntity
            'contractor_group', // Pivot table name
        )
        ->fields(['contractor_groups.id', 'contractor_groups.name'])
        ->filter([
            'contractor_group.contractor_id' => 'contractors.id',
            'contractor_group.group_id' => 'contractor_groups.id'
        ]);
    }

    public function details(): RelationBuilder
    {
        return $this->hasOne(ContractorDetailEntity::class, 'id', 'contractor_id');
        // ->fields(['inn', 'kpp']);
    }

    public function address(): RelationBuilder
    {
        return $this->hasOne(ContractorAddressEntity::class, 'id', 'contractor_id');
    }

    public function statuses(): RelationBuilder
    {
        return $this->hasOne(StatusEntity::class, 'status_id', 'id');
    }

    public function employees(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function departments(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }
}
