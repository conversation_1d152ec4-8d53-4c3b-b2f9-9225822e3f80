<?php

namespace App\Entities;

class ProductEntity extends BaseEntity
{
    public static string $table = 'products';

    public static array $fields = [
        'id',
        'archived_at',
        'cabinet_id',
        'title',
        'short_title',
        'type',
        'description',
        'short_description',
        'discounts_retail_sales',
        'product_group_id',
        'country_id',
        'article',
        'code',
        'inner_code',
        'external_code',
        'measurement_unit_id',
        'brand_id',
        'min_price',
        'min_price_currency_id',
        'purchase_price',
        'purchase_price_currency_id',
        'length',
        'width',
        'height',
        'weight',
        'volume',
        'tax_id',
        'tax_system',
        'indication_subject_calculation',
        'threshold_type',
        'threshold_count',
        'deleted_at',
        'created_at',
        'updated_at',
        'employee_id',
        'department_id',
        'contractor_id',
        'category_id',
        'is_default',

    ];

    public function employees(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function departments(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }

    public function contractors(): RelationBuilder
    {
        return $this->hasOne(ContractorEntity::class, 'contractor_id', 'id');
    }

    public function brands(): RelationBuilder
    {
        return $this->hasOne(BrandEntity::class, 'brand_id', 'id');
    }

    public function measurement_units(): RelationBuilder
    {
        return $this->hasOne(MeasurementUnitEntity::class, 'measurement_unit_id', 'id');
    }

    public function countries(): RelationBuilder
    {
        return $this->hasOne(CountryEntity::class, 'country_id', 'id');
    }

    public function price(): RelationBuilder
    {
        return $this->hasMany(ProductPriceEntity::class, 'id', 'product_id');
    }

    public function product_categories(): RelationBuilder
    {
        return $this->hasOne(ProductCategoryEntity::class, 'category_id', 'id');
    }

    public function cabinet_currencies(): RelationBuilder
    {
        return $this->hasOne(CabinetCurrencyEntity::class, 'min_price_currency_id', 'id');
    }

    public function feature(): RelationBuilder
    {
        return $this->hasOne(ProductAccountingFeatureEntity::class, 'id', 'product_id');
    }

    public function barcodes(): RelationBuilder
    {
        return $this->hasMany(BarcodeEntity::class, 'id', 'barcodable_id');
    }

    public function image(): RelationBuilder
    {
        return $this->hasOne(ProductImageEntity::class, 'id', 'product_id');
    }

}
