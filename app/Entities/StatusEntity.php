<?php

namespace App\Entities;

class StatusEntity extends BaseEntity
{
    public static string $table = 'statuses';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'deleted_at',
        'cabinet_id',
        'name',
        'color',
        'type_id',
    ];

    public function status_types(): RelationBuilder
    {
        return $this->hasOne(StatusTypeEntity::class, 'type_id', 'id');
    }
}
