<?php

namespace App\Entities;

class ProductAttributeEntity extends BaseEntity
{
    public static string $table = 'product_attributes';

    public static array $fields = [
        'id',
        'product_id',
        'attribute_id',
        'attribute_values_id',
        'copy',
        'sort_order',
    ];

    public function products(): RelationBuilder
    {
        return $this->hasOne(ProductEntity::class, 'product_id', 'id');
    }

    public function attributes(): RelationBuilder
    {
        return $this->hasOne(AttributeEntity::class, 'attribute_id', 'id');
    }

    public function attribute_values(): RelationBuilder
    {
        return $this->hasOne(AttributeValueEntity::class, 'attribute_values_id', 'id');
    }
}
