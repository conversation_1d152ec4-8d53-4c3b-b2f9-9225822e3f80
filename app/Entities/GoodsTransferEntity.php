<?php

namespace App\Entities;

class GoodsTransferEntity extends BaseEntity
{
    public static string $table = 'goods_transfers';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'cabinet_id',
        'employee_id',
        'department_id',
        'is_common',
        'number',
        'date_from',
        'status_id',
        'held',
        'legal_entity_id',
        'to_warehouse_id',
        'from_warehouse_id',
        'currency_id',
        'currency_value',
        'comment',
        'overhead_cost',
        'total_price',
    ];

    public function statuses(): RelationBuilder
    {
        return $this->hasOne(StatusEntity::class, 'status_id', 'id');
    }

    public function legal_entities(): RelationBuilder
    {
        return $this->hasOne(LegalEntity::class, 'legal_entity_id', 'id');
    }

    public function to_warehouses(): RelationBuilder
    {
        return $this->hasOne(WarehouseEntity::class, 'to_warehouse_id', 'id');
    }

    public function from_warehouses(): RelationBuilder
    {
        return $this->hasOne(WarehouseEntity::class, 'from_warehouse_id', 'id');
    }

    public function cabinet_currencies(): RelationBuilder
    {
        return $this->hasOne(CabinetCurrencyEntity::class, 'currency_id', 'id');
    }

    public function employees(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function departments(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }

    public function goods_transfer_items(): RelationBuilder
    {
        return $this->hasMany(GoodsTransferItemEntity::class, 'id', 'goods_transfer_id');
    }

    public function document(): RelationBuilder
    {
        return $this->hasOne(DocumentEntity::class, 'id', 'documentable_id');
    }
}
