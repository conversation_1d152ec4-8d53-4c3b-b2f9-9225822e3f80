<?php

namespace App\Entities;

class AcceptanceEntity extends BaseEntity
{
    public static string $table = 'acceptances';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'deleted_at',
        'cabinet_id',
        'employee_id',
        'department_id',
        'is_common',
        'number',
        'date_from',
        'status_id',
        'held',
        'legal_entity_id',
        'contractor_id',
        'warehouse_id',
        'incoming_number',
        'incoming_date',
        'currency_id',
        'currency_value',
        'comment',
        'price_includes_vat',
        'has_vat',
        'overhead_cost',
        'total_price',
    ];

    public function status(): RelationBuilder
    {
        return $this->hasOne(StatusEntity::class, 'status_id', 'id');
    }

    public function legalEntity(): RelationBuilder
    {
        return $this->hasOne(LegalEntity::class, 'legal_entity_id', 'id');
    }

    public function contractor(): RelationBuilder
    {
        return $this->hasOne(ContractorEntity::class, 'contractor_id', 'id');
    }

    public function warehouse(): RelationBuilder
    {
        return $this->hasOne(WarehouseEntity::class, 'warehouse_id', 'id');
    }

    public function currency(): RelationBuilder
    {
        return $this->hasOne(CabinetCurrencyEntity::class, 'currency_id', 'id');
    }

    public function employee(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function department(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }

    public function items(): RelationBuilder
    {
        return $this->hasMany(AcceptanceItemEntity::class, 'id', 'acceptance_id');
    }

    public function document(): RelationBuilder
    {
        return $this->hasOne(DocumentEntity::class, 'id', 'documentable_id');
    }
}
