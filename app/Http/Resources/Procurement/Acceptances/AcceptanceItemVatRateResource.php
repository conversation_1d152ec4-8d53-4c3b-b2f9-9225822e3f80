<?php

namespace App\Http\Resources\Procurement\Acceptances;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AcceptanceItemVatRateResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор ставки НДС */
            'id' => $this->id,
            /** @var int $rate Ставка НДС */
            'rate' => (int) $this->rate,
            /** @var string|null $description Описание ставки НДС */
            'description' => $this->description,
            /** @var bool $is_default Ставка по умолчанию */
            'is_default' => (bool) $this->is_default,
        ];
    }
}
