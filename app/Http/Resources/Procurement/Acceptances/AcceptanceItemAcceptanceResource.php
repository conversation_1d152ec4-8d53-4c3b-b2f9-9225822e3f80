<?php

namespace App\Http\Resources\Procurement\Acceptances;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AcceptanceItemAcceptanceResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор приемки */
            'id' => $this->id,
            /** @var string $number Номер приемки */
            'number' => $this->number,
            /** @var string $date_from Дата приемки */
            'date_from' => $this->date_from,
            /** @var bool $held Проведено */
            'held' => (bool) $this->held,
            /** @var string|null $incoming_number Входящий номер */
            'incoming_number' => $this->incoming_number,
            /** @var string|null $incoming_date Входящая дата */
            'incoming_date' => $this->incoming_date,
            /** @var string|null $comment Комментарий */
            'comment' => $this->comment,
        ];
    }
}
