<?php

namespace App\Http\Resources\Procurement\Acceptances;

use App\Http\Resources\Relations\AcceptanceRelationResource;
use App\Http\Resources\Relations\CountryRelationResource;
use App\Http\Resources\Relations\ProductRelationResource;
use App\Http\Resources\Relations\VatRateRelationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AcceptanceItemShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор записи */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->updated_at,
            /** @var string $acceptance_id Идентификатор приемки */
            'acceptance_id' => $this->acceptance_id,
            /** @var string $product_id Идентификатор товара */
            'product_id' => $this->product_id,
            /** @var int $quantity Количество товара */
            'quantity' => (int) $this->quantity,
            /** @var string $price Цена товара */
            'price' => (string) $this->price,
            /** @var string|null $vat_rate_id Идентификатор ставки НДС */
            'vat_rate_id' => $this->vat_rate_id,
            /** @var string $discount Размер скидки */
            'discount' => (string) ($this->discount ?? 0),
            /** @var string $total_price Общая стоимость */
            'total_price' => (string) $this->total_price,
            /** @var string|null $country_id Идентификатор страны */
            'country_id' => $this->country_id,
            /** @var string|null $gtd_number Номер ГТД */
            'gtd_number' => $this->gtd_number,
            /** @var int $recidual Остаток (вычисляемое поле) */
            'recidual' => (int) ($this->recidual ?? 0),

            /** @var \App\Http\Resources\Relations\AcceptanceRelationResource|null $acceptance Информация о приемке */
            'acceptance' => $this->whenLoaded('acceptance', function () {
                return new AcceptanceRelationResource($this->acceptance ?? []);
            }),
            /** @var \App\Http\Resources\Relations\ProductRelationResource|null $product Информация о товаре */
            'product' => $this->whenLoaded('product', function () {
                return new ProductRelationResource($this->product ?? []);
            }),
            /** @var \App\Http\Resources\Relations\VatRateRelationResource|null $vat_rate Информация о ставке НДС */
            'vat_rate' => $this->whenLoaded('vat_rate', function () {
                return new VatRateRelationResource($this->vat_rate ?? []);
            }),
            /** @var \App\Http\Resources\Relations\CountryRelationResource|null $country Информация о стране */
            'country' => $this->whenLoaded('country', function () {
                return new CountryRelationResource($this->country ?? []);
            }),
        ];
    }
}
