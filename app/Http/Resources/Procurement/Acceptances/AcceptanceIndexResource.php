<?php

namespace App\Http\Resources\Procurement\Acceptances;

use App\Http\Resources\Relations\AcceptanceItemRelationResource;
use App\Http\Resources\Relations\CabinetCurrencyRelationResource;
use App\Http\Resources\Relations\ContractorRelationResource;
use App\Http\Resources\Relations\DepartmentRelationResource;
use App\Http\Resources\Relations\DocumentRelationResource;
use App\Http\Resources\Relations\EmployeeRelationResource;
use App\Http\Resources\Relations\LegalEntityRelationResource;
use App\Http\Resources\Relations\StatusRelationResource;
use App\Http\Resources\Relations\WarehouseRelationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AcceptanceIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор записи */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->when(property_exists($this->resource, 'created_at'), function () {
                return $this->resource->created_at;
            }),
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->when(property_exists($this->resource, 'updated_at'), function () {
                return $this->resource->updated_at;
            }),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(property_exists($this->resource, 'deleted_at'), function () {
                return $this->resource->deleted_at;
            }),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(property_exists($this->resource, 'cabinet_id'), function () {
                return $this->resource->cabinet_id;
            }),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(property_exists($this->resource, 'employee_id'), function () {
                return $this->resource->employee_id;
            }),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(property_exists($this->resource, 'department_id'), function () {
                return $this->resource->department_id;
            }),
            /** @var bool $is_common Флаг общего доступа */
            'is_common' => $this->when(property_exists($this->resource, 'is_common'), function () {
                return (bool) $this->resource->is_common;
            }),
            /** @var string $number Номер документа */
            'number' => $this->when(property_exists($this->resource, 'number'), function () {
                return (string) $this->resource->number;
            }),
            /** @var string $date_from Дата начала действия */
            'date_from' => $this->when(property_exists($this->resource, 'date_from'), function () {
                return $this->resource->date_from;
            }),
            /** @var string|null $status_id Идентификатор статуса */
            'status_id' => $this->when(property_exists($this->resource, 'status_id'), function () {
                return $this->resource->status_id;
            }),
            /** @var bool $held Флаг удержания */
            'held' => $this->when(property_exists($this->resource, 'held'), function () {
                return (bool) $this->resource->held;
            }),
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->when(property_exists($this->resource, 'legal_entity_id'), function () {
                return $this->resource->legal_entity_id;
            }),
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->when(property_exists($this->resource, 'contractor_id'), function () {
                return $this->resource->contractor_id;
            }),
            /** @var string $warehouse_id Идентификатор склада */
            'warehouse_id' => $this->when(property_exists($this->resource, 'warehouse_id'), function () {
                return $this->resource->warehouse_id;
            }),
            /** @var string|null $incoming_number Входящий номер */
            'incoming_number' => $this->when(property_exists($this->resource, 'incoming_number'), function () {
                return $this->resource->incoming_number;
            }),
            /** @var string|null $incoming_date Дата поступления */
            'incoming_date' => $this->when(
                property_exists($this->resource, 'incoming_date'),
                function () {
                    return $this->resource->incoming_date;
                }
            ),
            /** @var string|null $currency_id Идентификатор валюты */
            'currency_id' => $this->when(
                property_exists($this->resource, 'currency_id'),
                function () {
                    return $this->resource->currency_id;
                }
            ),
            /** @var string|null $currency_value Значение валюты */
            'currency_value' => $this->when(
                property_exists($this->resource, 'currency_value'),
                function () {
                    return $this->resource->currency_value ? (string) $this->resource->currency_value : null;
                }
            ),
            /** @var string|null $comment Комментарий */
            'comment' => $this->when(
                property_exists($this->resource, 'comment'),
                function () {
                    return $this->resource->comment;
                }
            ),
            /** @var bool $price_includes_vat Цена включает НДС */
            'price_includes_vat' => $this->when(
                property_exists($this->resource, 'price_includes_vat'),
                function () {
                    return (bool) $this->resource->price_includes_vat;
                }
            ),
            /** @var bool $has_vat Наличие НДС */
            'has_vat' => $this->when(
                property_exists($this->resource, 'has_vat'),
                function () {
                    return (bool) $this->resource->has_vat;
                }
            ),
            /** @var string|null $overhead_cost Накладные расходы */
            'overhead_cost' => $this->when(property_exists($this->resource, 'overhead_cost'), function () {
                return $this->resource->overhead_cost ? (string) $this->resource->overhead_cost : null;
            }),
            /** @var string $total_price Общая стоимость */
            'total_price' => $this->when(property_exists($this->resource, 'total_price'), function () {
                return (string) $this->resource->total_price;
            }),
        ];
    }
}
