<?php

namespace App\Http\Resources\Procurement\Acceptances;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AcceptanceItemProductResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор товара */
            'id' => $this->id,
            /** @var string $title Название товара */
            'title' => $this->title,
            /** @var string|null $code Код товара */
            'code' => $this->code,
            /** @var string|null $article Артикул товара */
            'article' => $this->article,
            /** @var string|null $external_code Внешний код товара */
            'external_code' => $this->external_code,
            /** @var string|null $description Описание товара */
            'description' => $this->description,
            /** @var string|null $type Тип товара */
            'type' => $this->type,
        ];
    }
}
