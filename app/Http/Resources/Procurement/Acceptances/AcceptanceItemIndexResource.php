<?php

namespace App\Http\Resources\Procurement\Acceptances;

use App\Http\Resources\Relations\AcceptanceRelationResource;
use App\Http\Resources\Relations\CountryRelationResource;
use App\Http\Resources\Relations\ProductRelationResource;
use App\Http\Resources\Relations\VatRateRelationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AcceptanceItemIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор записи */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->when(property_exists($this->resource, 'created_at'), function () {
                return $this->resource->created_at;
            }),
            /** @var string|null $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->when(property_exists($this->resource, 'updated_at'), function () {
                return $this->resource->updated_at;
            }),
            /** @var string $acceptance_id Идентификатор приемки */
            'acceptance_id' => $this->when(property_exists($this->resource, 'acceptance_id'), function () {
                return $this->resource->acceptance_id;
            }),
            /** @var string $product_id Идентификатор товара */
            'product_id' => $this->when(property_exists($this->resource, 'product_id'), function () {
                return $this->resource->product_id;
            }),
            /** @var int $quantity Количество товара */
            'quantity' => $this->when(property_exists($this->resource, 'quantity'), function () {
                return (int) $this->resource->quantity;
            }),
            /** @var string $price Цена товара */
            'price' => $this->when(property_exists($this->resource, 'price'), function () {
                return (string) $this->resource->price;
            }),
            /** @var string|null $vat_rate_id Идентификатор ставки НДС */
            'vat_rate_id' => $this->when(property_exists($this->resource, 'vat_rate_id'), function () {
                return $this->resource->vat_rate_id;
            }),
            /** @var string $discount Размер скидки */
            'discount' => $this->when(property_exists($this->resource, 'discount'), function () {
                return (string) ($this->resource->discount ?? '0');
            }),
            /** @var string $total_price Общая стоимость */
            'total_price' => $this->when(property_exists($this->resource, 'total_price'), function () {
                return (string) $this->resource->total_price;
            }),
            /** @var string|null $country_id Идентификатор страны */
            'country_id' => $this->when(property_exists($this->resource, 'country_id'), function () {
                return $this->resource->country_id;
            }),
            /** @var string|null $gtd_number Номер ГТД */
            'gtd_number' => $this->when(property_exists($this->resource, 'gtd_number'), function () {
                return $this->resource->gtd_number;
            }),
            /** @var int $recidual Остаток (вычисляемое поле) */
            'recidual' => $this->when(property_exists($this->resource, 'recidual'), function () {
                return (int) ($this->resource->recidual ?? 0);
            }),
            /** @var AcceptanceRelationResource|null $acceptance Информация о приемке */
            'acceptance' => $this->when(property_exists($this->resource, 'acceptance'), function () {
                return new AcceptanceRelationResource($this->resource->acceptance ?? []);
            }),
            /** @var ProductRelationResource|null $product Информация о товаре */
            'product' => $this->when(property_exists($this->resource, 'product'), function () {
                return new ProductRelationResource($this->resource->product ?? []);
            }),
            /** @var VatRateRelationResource|null $vat_rate Информация о ставке НДС */
            'vat_rate' => $this->when(property_exists($this->resource, 'vat_rate'), function () {
                return new VatRateRelationResource($this->resource->vat_rate ?? []);
            }),
            /** @var CountryRelationResource|null $country Информация о стране */
            'country' => $this->when(property_exists($this->resource, 'country'), function () {
                return new CountryRelationResource($this->resource->country ?? []);
            }),
        ];
    }
}
