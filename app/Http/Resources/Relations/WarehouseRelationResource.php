<?php

namespace App\Http\Resources\Relations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WarehouseRelationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор склада */
            'id' => $this->when(isset($this->resource['id']), function () {
                return $this->resource['id'];
            }),
            /** @var string|null $created_at Дата создания */
            'created_at' => $this->when(isset($this->resource['created_at']), function () {
                return $this->resource['created_at'];
            }),
            /** @var string|null $updated_at Дата обновления */
            'updated_at' => $this->when(isset($this->resource['updated_at']), function () {
                return $this->resource['updated_at'];
            }),
            /** @var string|null $deleted_at Дата удаления */
            'deleted_at' => $this->when(isset($this->resource['deleted_at']), function () {
                return $this->resource['deleted_at'];
            }),
            /** @var string|null $archived_at Дата архивирования */
            'archived_at' => $this->when(isset($this->resource['archived_at']), function () {
                return $this->resource['archived_at'];
            }),
            /** @var string $name Название склада */
            'name' => $this->when(isset($this->resource['name']), function () {
                return $this->resource['name'];
            }),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->resource['cabinet_id']), function () {
                return $this->resource['cabinet_id'];
            }),
            /** @var string|null $work_schedule_id Идентификатор рабочего расписания */
            'work_schedule_id' => $this->when(isset($this->resource['work_schedule_id']), function () {
                return $this->resource['work_schedule_id'];
            }),
            /** @var bool|null $control_free_residuals Контроль свободных остатков */
            'control_free_residuals' => $this->when(isset($this->resource['control_free_residuals']), function () {
                return (bool) $this->resource['control_free_residuals'];
            }),
            /** @var string|null $responsible_employee_id Идентификатор ответственного сотрудника */
            'responsible_employee_id' => $this->when(isset($this->resource['responsible_employee_id']), function () {
                return $this->resource['responsible_employee_id'];
            }),
            /** @var string|null $address_id Идентификатор адреса */
            'address_id' => $this->when(isset($this->resource['address_id']), function () {
                return $this->resource['address_id'];
            }),
            /** @var string|null $phone_id Идентификатор телефона */
            'phone_id' => $this->when(isset($this->resource['phone_id']), function () {
                return $this->resource['phone_id'];
            }),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(isset($this->resource['employee_id']), function () {
                return $this->resource['employee_id'];
            }),
            /** @var bool $is_common Общий */
            'is_common' => $this->when(isset($this->resource['is_common']), function () {
                return (bool) $this->resource['is_common'];
            }),
            /** @var bool $is_default По умолчанию */
            'is_default' => $this->when(isset($this->resource['is_default']), function () {
                return (bool) $this->resource['is_default'];
            }),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(isset($this->resource['department_id']), function () {
                return $this->resource['department_id'];
            }),
            /** @var string|null $group_id Идентификатор группы */
            'group_id' => $this->when(isset($this->resource['group_id']), function () {
                return $this->resource['group_id'];
            }),
        ];
    }
}
