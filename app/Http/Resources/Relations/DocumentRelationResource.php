<?php

namespace App\Http\Resources\Relations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DocumentRelationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $documentable_id Идентификатор документа */
            'documentable_id' => $this->when(isset($this->resource['documentable_id']), function () {
                return $this->resource['documentable_id'];
            }),
            /** @var string|null $created_at Дата создания */
            'created_at' => $this->when(isset($this->resource['created_at']), function () {
                return $this->resource['created_at'];
            }),
            /** @var string|null $updated_at Дата обновления */
            'updated_at' => $this->when(isset($this->resource['updated_at']), function () {
                return $this->resource['updated_at'];
            }),
            /** @var string|null $deleted_at Дата удаления */
            'deleted_at' => $this->when(isset($this->resource['deleted_at']), function () {
                return $this->resource['deleted_at'];
            }),
            /** @var string $documentable_type Тип документа */
            'documentable_type' => $this->when(isset($this->resource['documentable_type']), function () {
                return $this->resource['documentable_type'];
            }),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->resource['cabinet_id']), function () {
                return $this->resource['cabinet_id'];
            }),
            /** @var string $tree_id Идентификатор дерева */
            'tree_id' => $this->when(isset($this->resource['tree_id']), function () {
                return $this->resource['tree_id'];
            }),
            /** @var int $lft Левая граница */
            'lft' => $this->when(isset($this->resource['lft']), function () {
                return (int) $this->resource['lft'];
            }),
            /** @var int $rgt Правая граница */
            'rgt' => $this->when(isset($this->resource['rgt']), function () {
                return (int) $this->resource['rgt'];
            }),
            /** @var string|null $parent_id Идентификатор родителя */
            'parent_id' => $this->when(isset($this->resource['parent_id']), function () {
                return $this->resource['parent_id'];
            }),
        ];
    }
}
