<?php

namespace App\Http\Resources\Relations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AcceptanceItemRelationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор элемента приемки */
            'id' => $this->when(isset($this->resource['id']), function () {
                return $this->resource['id'];
            }),
            /** @var string|null $created_at Дата создания */
            'created_at' => $this->when(isset($this->resource['created_at']), function () {
                return $this->resource['created_at'];
            }),
            /** @var string|null $updated_at Дата обновления */
            'updated_at' => $this->when(isset($this->resource['updated_at']), function () {
                return $this->resource['updated_at'];
            }),
            /** @var string $acceptance_id Идентификатор приемки */
            'acceptance_id' => $this->when(isset($this->resource['acceptance_id']), function () {
                return $this->resource['acceptance_id'];
            }),
            /** @var string $product_id Идентификатор товара */
            'product_id' => $this->when(isset($this->resource['product_id']), function () {
                return $this->resource['product_id'];
            }),
            /** @var int $quantity Количество */
            'quantity' => $this->when(isset($this->resource['quantity']), function () {
                return (int) $this->resource['quantity'];
            }),
            /** @var string $price Цена */
            'price' => $this->when(isset($this->resource['price']), function () {
                return (string) $this->resource['price'];
            }),
            /** @var string|null $vat_rate_id Идентификатор ставки НДС */
            'vat_rate_id' => $this->when(isset($this->resource['vat_rate_id']), function () {
                return $this->resource['vat_rate_id'];
            }),
            /** @var string $discount Скидка */
            'discount' => $this->when(isset($this->resource['discount']), function () {
                return (string) $this->resource['discount'];
            }),
            /** @var string $total_price Общая стоимость */
            'total_price' => $this->when(isset($this->resource['total_price']), function () {
                return (string) $this->resource['total_price'];
            }),
            /** @var string|null $country_id Идентификатор страны */
            'country_id' => $this->when(isset($this->resource['country_id']), function () {
                return $this->resource['country_id'];
            }),
            /** @var string|null $gtd_number Номер ГТД */
            'gtd_number' => $this->when(isset($this->resource['gtd_number']), function () {
                return $this->resource['gtd_number'];
            }),
            /** @var int $recidual Остаток */
            'recidual' => $this->when(isset($this->resource['recidual']), function () {
                return (int) $this->resource['recidual'];
            }),
        ];
    }
}
