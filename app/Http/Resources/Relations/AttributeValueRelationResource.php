<?php

namespace App\Http\Resources\Relations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttributeValueRelationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор значения атрибута */
            'id' => $this->when(isset($this->resource['id']), function () {
                return $this->resource['id'];
            }),
            /** @var string $attribute_id Идентификатор атрибута */
            'attribute_id' => $this->when(isset($this->resource['attribute_id']), function () {
                return $this->resource['attribute_id'];
            }),
            /** @var string $value Значение атрибута */
            'value' => $this->when(isset($this->resource['value']), function () {
                return $this->resource['value'];
            }),
        ];
    }
}
