<?php

namespace App\Http\Resources\Relations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AcceptanceRelationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор приемки */
            'id' => $this->when(isset($this->resource['id']), function () {
                return $this->resource['id'];
            }),
            /** @var string|null $created_at Дата создания */
            'created_at' => $this->when(isset($this->resource['created_at']), function () {
                return $this->resource['created_at'];
            }),
            /** @var string|null $updated_at Дата обновления */
            'updated_at' => $this->when(isset($this->resource['updated_at']), function () {
                return $this->resource['updated_at'];
            }),
            /** @var string|null $deleted_at Дата удаления */
            'deleted_at' => $this->when(isset($this->resource['deleted_at']), function () {
                return $this->resource['deleted_at'];
            }),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->resource['cabinet_id']), function () {
                return $this->resource['cabinet_id'];
            }),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(isset($this->resource['employee_id']), function () {
                return $this->resource['employee_id'];
            }),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(isset($this->resource['department_id']), function () {
                return $this->resource['department_id'];
            }),
            /** @var bool $is_common Общий документ */
            'is_common' => $this->when(isset($this->resource['is_common']), function () {
                return (bool) $this->resource['is_common'];
            }),
            /** @var string $number Номер приемки */
            'number' => $this->when(isset($this->resource['number']), function () {
                return $this->resource['number'];
            }),
            /** @var string $date_from Дата приемки */
            'date_from' => $this->when(isset($this->resource['date_from']), function () {
                return $this->resource['date_from'];
            }),
            /** @var string|null $status_id Идентификатор статуса */
            'status_id' => $this->when(isset($this->resource['status_id']), function () {
                return $this->resource['status_id'];
            }),
            /** @var bool $held Проведено */
            'held' => $this->when(isset($this->resource['held']), function () {
                return (bool) $this->resource['held'];
            }),
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->when(isset($this->resource['legal_entity_id']), function () {
                return $this->resource['legal_entity_id'];
            }),
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->when(isset($this->resource['contractor_id']), function () {
                return $this->resource['contractor_id'];
            }),
            /** @var string $warehouse_id Идентификатор склада */
            'warehouse_id' => $this->when(isset($this->resource['warehouse_id']), function () {
                return $this->resource['warehouse_id'];
            }),
            /** @var string|null $incoming_number Входящий номер */
            'incoming_number' => $this->when(isset($this->resource['incoming_number']), function () {
                return $this->resource['incoming_number'];
            }),
            /** @var string|null $incoming_date Входящая дата */
            'incoming_date' => $this->when(isset($this->resource['incoming_date']), function () {
                return $this->resource['incoming_date'];
            }),
            /** @var string $currency_id Идентификатор валюты */
            'currency_id' => $this->when(isset($this->resource['currency_id']), function () {
                return $this->resource['currency_id'];
            }),
            /** @var string $currency_value Курс валюты */
            'currency_value' => $this->when(isset($this->resource['currency_value']), function () {
                return (string) $this->resource['currency_value'];
            }),
            /** @var string|null $comment Комментарий */
            'comment' => $this->when(isset($this->resource['comment']), function () {
                return $this->resource['comment'];
            }),
            /** @var bool $price_includes_vat Цена включает НДС */
            'price_includes_vat' => $this->when(isset($this->resource['price_includes_vat']), function () {
                return (bool) $this->resource['price_includes_vat'];
            }),
            /** @var bool $has_vat Есть НДС */
            'has_vat' => $this->when(isset($this->resource['has_vat']), function () {
                return (bool) $this->resource['has_vat'];
            }),
            /** @var string $overhead_cost Накладные расходы */
            'overhead_cost' => $this->when(isset($this->resource['overhead_cost']), function () {
                return (string) $this->resource['overhead_cost'];
            }),
            /** @var string $total_price Общая цена */
            'total_price' => $this->when(isset($this->resource['total_price']), function () {
                return (string) $this->resource['total_price'];
            }),
        ];
    }
}
