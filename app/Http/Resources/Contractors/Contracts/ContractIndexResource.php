<?php

namespace App\Http\Resources\Contractors\Contracts;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор контракта */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->when(
                property_exists($this->resource, 'archived_at'),
                function() {
                    return $this->resource->archived_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $number Номер контракта */
            'number' => $this->when(
                property_exists($this->resource, 'number'),
                function() {
                    return (string) $this->resource->number;
                }
            ),
            /** @var string $date_from Дата контракта */
            'date_from' => $this->when(
                property_exists($this->resource, 'date_from'),
                function() {
                    return $this->resource->date_from;
                }
            ),
            /** @var string|null $status_id Идентификатор статуса */
            'status_id' => $this->when(
                property_exists($this->resource, 'status_id'),
                function() {
                    return $this->resource->status_id;
                }
            ),
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->when(
                property_exists($this->resource, 'legal_entity_id'),
                function() {
                    return $this->resource->legal_entity_id;
                }
            ),
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->when(
                property_exists($this->resource, 'contractor_id'),
                function() {
                    return $this->resource->contractor_id;
                }
            ),
            /** @var string $currency_id Идентификатор валюты */
            'currency_id' => $this->when(
                property_exists($this->resource, 'currency_id'),
                function() {
                    return $this->resource->currency_id;
                }
            ),
            /** @var string $type Тип контракта */
            'type' => $this->when(
                property_exists($this->resource, 'type'),
                function() {
                    return (string) $this->resource->type;
                }
            ),
            /** @var string|null $code Код */
            'code' => $this->when(
                property_exists($this->resource, 'code'),
                function() {
                    return $this->resource->code;
                }
            ),
            /** @var string|null $amount Сумма */
            'amount' => $this->when(
                property_exists($this->resource, 'amount'),
                function() {
                    return $this->resource->amount ? (string) $this->resource->amount : null;
                }
            ),
            /** @var string|null $comment Комментарий */
            'comment' => $this->when(
                property_exists($this->resource, 'comment'),
                function() {
                    return $this->resource->comment;
                }
            ),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function() {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
            /** @var bool $shared_access Общий доступ */
            'shared_access' => $this->when(
                property_exists($this->resource, 'shared_access'),
                function() {
                    return (bool) $this->resource->shared_access;
                }
            ),
            /** @var bool $is_printed Напечатан */
            'is_printed' => $this->when(
                property_exists($this->resource, 'is_printed'),
                function() {
                    return (bool) $this->resource->is_printed;
                }
            ),
            /** @var bool $is_sended Отправлен */
            'is_sended' => $this->when(
                property_exists($this->resource, 'is_sended'),
                function() {
                    return (bool) $this->resource->is_sended;
                }
            ),
            /** @var array{id: string, name: string, color: string}|null $status Статус */
            'status' => $this->when(
                property_exists($this->resource, 'status'),
                function() {
                    return !empty($this->status) ? $this->status : (object) [];
                }
            ),
            /** @var array{id: string, title: string}|null $contractor Контрагент */
            'contractor' => $this->when(
                property_exists($this->resource, 'contractor'),
                function() {
                    return !empty($this->contractor) ? $this->contractor : (object) [];
                }
            ),
        ];
    }
}
