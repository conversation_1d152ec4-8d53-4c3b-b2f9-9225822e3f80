<?php

namespace App\Http\Resources\Contractors;

use App\Http\Resources\Relations\DepartmentRelationResource;
use App\Http\Resources\Relations\EmployeeRelationResource;
use App\Http\Resources\Relations\StatusRelationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractorIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор контрагента */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function() {
                    return $this->resource->deleted_at;
                }
            ),
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->when(
                property_exists($this->resource, 'archived_at'),
                function() {
                    return $this->resource->archived_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var bool $shared_access Общий доступ */
            'shared_access' => $this->when(
                property_exists($this->resource, 'shared_access'),
                function() {
                    return (bool) $this->resource->shared_access;
                }
            ),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function() {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
            /** @var string $title Наименование */
            'title' => $this->when(
                property_exists($this->resource, 'title'),
                function() {
                    return (string) $this->resource->title;
                }
            ),
            /** @var string $status_id Идентификатор статуса */
            'status_id' => $this->when(
                property_exists($this->resource, 'status_id'),
                function() {
                    return $this->resource->status_id;
                }
            ),
            /** @var bool $is_buyer Покупатель */
            'is_buyer' => $this->when(
                property_exists($this->resource, 'is_buyer'),
                function() {
                    return (bool) $this->resource->is_buyer;
                }
            ),
            /** @var bool $is_supplier Поставщик */
            'is_supplier' => $this->when(
                property_exists($this->resource, 'is_supplier'),
                function() {
                    return (bool) $this->resource->is_supplier;
                }
            ),
            /** @var string|null $phone Телефон */
            'phone' => $this->when(
                property_exists($this->resource, 'phone'),
                function() {
                    return $this->resource->phone;
                }
            ),
            /** @var string|null $fax Факс */
            'fax' => $this->when(
                property_exists($this->resource, 'fax'),
                function() {
                    return $this->resource->fax;
                }
            ),
            /** @var string|null $email Электронный адрес */
            'email' => $this->when(
                property_exists($this->resource, 'email'),
                function() {
                    return $this->resource->email;
                }
            ),
            /** @var string|null $description Комментарий */
            'description' => $this->when(
                property_exists($this->resource, 'description'),
                function() {
                    return $this->resource->description;
                }
            ),
            /** @var string|null $code Код */
            'code' => $this->when(
                property_exists($this->resource, 'code'),
                function() {
                    return $this->resource->code;
                }
            ),
            /** @var string|null $external_code Внешний код */
            'external_code' => $this->when(
                property_exists($this->resource, 'external_code'),
                function() {
                    return $this->resource->external_code;
                }
            ),
            /** @var string|null $discounts_and_prices Скидки и цены */
            'discounts_and_prices' => $this->when(
                property_exists($this->resource, 'discounts_and_prices'),
                function() {
                    return $this->resource->discounts_and_prices;
                }
            ),
            /** @var string|null $discount_card_number Номер дисконтной карты */
            'discount_card_number' => $this->when(
                property_exists($this->resource, 'discount_card_number'),
                function() {
                    return $this->resource->discount_card_number;
                }
            ),
            /** @var bool $is_default Контрагент по умолчанию */
            'is_default' => $this->when(
                property_exists($this->resource, 'is_default'),
                function() {
                    return (bool) $this->resource->is_default;
                }
            )
        ];
    }
}
