<?php

namespace App\Http\Resources\Goods\Transfers;

use App\Http\Resources\Relations\CabinetCurrencyRelationResource;
use App\Http\Resources\Relations\DepartmentRelationResource;
use App\Http\Resources\Relations\EmployeeRelationResource;
use App\Http\Resources\Relations\LegalEntityRelationResource;
use App\Http\Resources\Relations\StatusRelationResource;
use App\Http\Resources\Relations\WarehouseRelationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GoodsTransferIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор перемещения товаров */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function() {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
            /** @var bool $is_common Общее перемещение */
            'is_common' => $this->when(
                property_exists($this->resource, 'is_common'),
                function() {
                    return (bool) $this->resource->is_common;
                }
            ),
            /** @var string|null $number Номер перемещения */
            'number' => $this->when(
                property_exists($this->resource, 'number'),
                function() {
                    return $this->resource->number;
                }
            ),
            /** @var string $date_from Дата перемещения */
            'date_from' => $this->when(
                property_exists($this->resource, 'date_from'),
                function() {
                    return $this->resource->date_from;
                }
            ),
            /** @var string|null $status_id Идентификатор статуса */
            'status_id' => $this->when(
                property_exists($this->resource, 'status_id'),
                function() {
                    return $this->resource->status_id;
                }
            ),
            /** @var bool $held Проведено */
            'held' => $this->when(
                property_exists($this->resource, 'held'),
                function() {
                    return (bool) $this->resource->held;
                }
            ),
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->when(
                property_exists($this->resource, 'legal_entity_id'),
                function() {
                    return $this->resource->legal_entity_id;
                }
            ),
            /** @var string $to_warehouse_id Идентификатор склада назначения */
            'to_warehouse_id' => $this->when(
                property_exists($this->resource, 'to_warehouse_id'),
                function() {
                    return $this->resource->to_warehouse_id;
                }
            ),
            /** @var string $from_warehouse_id Идентификатор склада отправления */
            'from_warehouse_id' => $this->when(
                property_exists($this->resource, 'from_warehouse_id'),
                function() {
                    return $this->resource->from_warehouse_id;
                }
            ),
            /** @var string $currency_id Идентификатор валюты */
            'currency_id' => $this->when(
                property_exists($this->resource, 'currency_id'),
                function() {
                    return $this->resource->currency_id;
                }
            ),
            /** @var string $currency_value Курс валюты */
            'currency_value' => $this->when(
                property_exists($this->resource, 'currency_value'),
                function() {
                    return (string) $this->resource->currency_value;
                }
            ),
            /** @var string|null $comment Комментарий */
            'comment' => $this->when(
                property_exists($this->resource, 'comment'),
                function() {
                    return $this->resource->comment;
                }
            ),
            /** @var string $overhead_cost Накладные расходы */
            'overhead_cost' => $this->when(
                property_exists($this->resource, 'overhead_cost'),
                function() {
                    return (string) $this->resource->overhead_cost;
                }
            ),
            /** @var string $total_price Общая стоимость */
            'total_price' => $this->when(
                property_exists($this->resource, 'total_price'),
                function() {
                    return (string) $this->resource->total_price;
                }
            ),
        ];
    }
}
