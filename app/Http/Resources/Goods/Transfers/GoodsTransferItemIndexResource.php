<?php

namespace App\Http\Resources\Goods\Transfers;

use App\Http\Resources\Relations\GoodsTransferRelationResource;
use App\Http\Resources\Relations\ProductRelationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GoodsTransferItemIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор позиции перемещения товаров */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function () {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function () {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $goods_transfer_id Идентификатор перемещения товаров */
            'goods_transfer_id' => $this->when(
                property_exists($this->resource, 'goods_transfer_id'),
                function () {
                    return $this->resource->goods_transfer_id;
                }
            ),
            /** @var string $product_id Идентификатор товара */
            'product_id' => $this->when(
                property_exists($this->resource, 'product_id'),
                function () {
                    return $this->resource->product_id;
                }
            ),
            /** @var int $quantity Количество */
            'quantity' => $this->when(
                property_exists($this->resource, 'quantity'),
                function () {
                    return (int) $this->resource->quantity;
                }
            ),
            /** @var string $price Цена */
            'price' => $this->when(
                property_exists($this->resource, 'price'),
                function () {
                    return (string) $this->resource->price;
                }
            ),
            /** @var string $total_price Общая стоимость */
            'total_price' => $this->when(
                property_exists($this->resource, 'total_price'),
                function () {
                    return (string) $this->resource->total_price;
                }
            ),
            /** @var string $recidual_from Остаток с */
            'recidual_from' => $this->when(
                property_exists($this->resource, 'recidual_from'),
                function () {
                    return (string) $this->resource->recidual_from;
                }
            ),
            /** @var string $recidual_to Остаток по */
            'recidual_to' => $this->when(
                property_exists($this->resource, 'recidual_to'),
                function () {
                    return (string) $this->resource->recidual_to;
                }
            ),

            /** @var ProductRelationResource|null $products Информация о товаре */
            'products' => $this->when(property_exists($this->resource, 'products'), function () {
                return new ProductRelationResource($this->resource->products ?? []);
            }),
            /** @var GoodsTransferRelationResource|null $goods_transfers Информация о перемещении */
            'goods_transfers' => $this->when(property_exists($this->resource, 'goods_transfers'), function () {
                return new GoodsTransferRelationResource($this->resource->goods_transfers ?? []);
            }),
        ];
    }
}
