<?php

namespace App\Http\Resources\Goods\Products;

use App\Http\Resources\Relations\BrandRelationResource;
use App\Http\Resources\Relations\CabinetCurrencyRelationResource;
use App\Http\Resources\Relations\ContractorRelationResource;
use App\Http\Resources\Relations\CountryRelationResource;
use App\Http\Resources\Relations\DepartmentRelationResource;
use App\Http\Resources\Relations\EmployeeRelationResource;
use App\Http\Resources\Relations\MeasurementUnitRelationResource;
use App\Http\Resources\Relations\ProductCategoryRelationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор товара */
            'id' => $this->id,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->when(
                property_exists($this->resource, 'archived_at'),
                function () {
                    return $this->resource->archived_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function () {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $title Название товара */
            'title' => $this->when(
                property_exists($this->resource, 'title'),
                function () {
                    return (string) $this->resource->title;
                }
            ),
            /** @var string $short_title Короткое название */
            'short_title' => $this->when(
                property_exists($this->resource, 'short_title'),
                function () {
                    return (string) $this->resource->short_title;
                }
            ),
            /** @var int $type Тип товара */
            'type' => $this->when(
                property_exists($this->resource, 'type'),
                function () {
                    return (int) $this->resource->type;
                }
            ),
            /** @var string|null $description Описание */
            'description' => $this->when(
                property_exists($this->resource, 'description'),
                function () {
                    return $this->resource->description;
                }
            ),
            /** @var string|null $short_description Короткое описание */
            'short_description' => $this->when(
                property_exists($this->resource, 'short_description'),
                function () {
                    return $this->resource->short_description;
                }
            ),
            /** @var bool $discounts_retail_sales Запретить скидки при продаже в розницу */
            'discounts_retail_sales' => $this->when(
                property_exists($this->resource, 'discounts_retail_sales'),
                function () {
                    return (bool) $this->resource->discounts_retail_sales;
                }
            ),
            /** @var string|null $product_group_id Идентификатор группы товаров */
            'product_group_id' => $this->when(
                property_exists($this->resource, 'product_group_id'),
                function () {
                    return $this->resource->product_group_id;
                }
            ),
            /** @var string|null $country_id Идентификатор страны */
            'country_id' => $this->when(
                property_exists($this->resource, 'country_id'),
                function () {
                    return $this->resource->country_id;
                }
            ),
            /** @var string|null $article Артикул */
            'article' => $this->when(
                property_exists($this->resource, 'article'),
                function () {
                    return $this->resource->article;
                }
            ),
            /** @var string|null $code Код товара */
            'code' => $this->when(
                property_exists($this->resource, 'code'),
                function () {
                    return $this->resource->code;
                }
            ),
            /** @var int|null $inner_code Внутренний код */
            'inner_code' => $this->when(
                property_exists($this->resource, 'inner_code'),
                function () {
                    return $this->resource->inner_code ? (int) $this->resource->inner_code : null;
                }
            ),
            /** @var string|null $external_code Внешний код */
            'external_code' => $this->when(
                property_exists($this->resource, 'external_code'),
                function () {
                    return $this->resource->external_code;
                }
            ),
            /** @var string|null $measurement_unit_id Идентификатор единицы измерения */
            'measurement_unit_id' => $this->when(
                property_exists($this->resource, 'measurement_unit_id'),
                function () {
                    return $this->resource->measurement_unit_id;
                }
            ),
            /** @var string|null $brand_id Идентификатор бренда */
            'brand_id' => $this->when(
                property_exists($this->resource, 'brand_id'),
                function () {
                    return $this->resource->brand_id;
                }
            ),
            /** @var object{value: string, currency_id: string} $min_price Минимальная цена */
            'min_price' => $this->when(
                property_exists($this->resource, 'min_price'),
                function () {
                    return [
                        'value' => (string) $this->resource->min_price,
                        'currency_id' => $this->resource->min_price_currency_id,
                    ];
                }
            ),
            /** @var object{value: string, currency_id: string} $purchase_price Закупочная цена */
            'purchase_price' => $this->when(
                property_exists($this->resource, 'purchase_price'),
                function () {
                    return [
                        'value' => (string) $this->resource->purchase_price,
                        'currency_id' => $this->resource->purchase_price_currency_id,
                    ];
                }
            ),
            /** @var array{length: string, width: string, height: string, weight: string, volume: string} $dimensions Габариты */
            'dimensions' => $this->when(
                property_exists($this->resource, 'length'),
                function () {
                    return [
                        'length' => (string) $this->resource->length,
                        'width' => (string) $this->resource->width,
                        'height' => (string) $this->resource->height,
                        'weight' => (string) $this->resource->weight,
                        'volume' => (string) $this->resource->volume,
                    ];
                }
            ),
            /** @var string|null $tax_id Идентификатор налога */
            'tax_id' => $this->when(
                property_exists($this->resource, 'tax_id'),
                function () {
                    return $this->resource->tax_id;
                }
            ),
            /** @var string|null $tax_system Система налогообложения */
            'tax_system' => $this->when(
                property_exists($this->resource, 'tax_system'),
                function () {
                    return $this->resource->tax_system;
                }
            ),
            /** @var string|null $indication_subject_calculation Признак предмета расчета */
            'indication_subject_calculation' => $this->when(
                property_exists($this->resource, 'indication_subject_calculation'),
                function () {
                    return $this->resource->indication_subject_calculation;
                }
            ),
            /** @var int|null $threshold_type Тип порога */
            'threshold_type' => $this->when(
                property_exists($this->resource, 'threshold_type'),
                function () {
                    return $this->resource->threshold_type ? (int) $this->resource->threshold_type : null;
                }
            ),
            /** @var int|null $threshold_count Количество порога */
            'threshold_count' => $this->when(
                property_exists($this->resource, 'threshold_count'),
                function () {
                    return $this->resource->threshold_count ? (int) $this->resource->threshold_count : null;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function () {
                    return $this->resource->deleted_at;
                }
            ),
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function () {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function () {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function () {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function () {
                    return $this->resource->department_id;
                }
            ),
            /** @var string|null $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->when(
                property_exists($this->resource, 'contractor_id'),
                function () {
                    return $this->resource->contractor_id;
                }
            ),
            /** @var string|null $category_id Идентификатор категории */
            'category_id' => $this->when(
                property_exists($this->resource, 'category_id'),
                function () {
                    return $this->resource->category_id;
                }
            ),
            /** @var bool $is_default Товар по умолчанию */
            'is_default' => $this->when(
                property_exists($this->resource, 'is_default'),
                function () {
                    return (bool) $this->resource->is_default;
                }
            ),
        ];
    }
}
