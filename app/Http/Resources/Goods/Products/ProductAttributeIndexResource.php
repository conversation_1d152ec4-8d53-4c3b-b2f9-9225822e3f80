<?php

namespace App\Http\Resources\Goods\Products;

use App\Http\Resources\Relations\AttributeRelationResource;
use App\Http\Resources\Relations\AttributeValueRelationResource;
use App\Http\Resources\Relations\ProductRelationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductAttributeIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор атрибута товара */
            'id' => $this->id,
            /** @var string $product_id Идентификатор товара */
            'product_id' => $this->when(
                property_exists($this->resource, 'product_id'),
                function () {
                    return $this->resource->product_id;
                }
            ),
            /** @var string $attribute_id Идентификатор атрибута */
            'attribute_id' => $this->when(
                property_exists($this->resource, 'attribute_id'),
                function () {
                    return $this->resource->attribute_id;
                }
            ),
            /** @var string $attribute_values_id Идентификатор значения атрибута */
            'attribute_values_id' => $this->when(
                property_exists($this->resource, 'attribute_values_id'),
                function () {
                    return $this->resource->attribute_values_id;
                }
            ),
            /** @var bool $copy Копия */
            'copy' => $this->when(
                property_exists($this->resource, 'copy'),
                function () {
                    return (bool) $this->resource->copy;
                }
            ),
            /** @var int|null $sort_order Порядок сортировки */
            'sort_order' => $this->when(
                property_exists($this->resource, 'sort_order'),
                function () {
                    return $this->resource->sort_order ? (int) $this->resource->sort_order : null;
                }
            ),

            /** @var ProductRelationResource|null $products Информация о товаре */
            'products' => $this->when(property_exists($this->resource, 'products'), function () {
                return new ProductRelationResource($this->resource->products ?? []);
            }),
            /** @var AttributeRelationResource|null $attributes Информация об атрибуте */
            'attributes' => $this->when(property_exists($this->resource, 'attributes'), function () {
                return new AttributeRelationResource($this->resource->attributes ?? []);
            }),
            /** @var AttributeValueRelationResource|null $attribute_values Информация о значении атрибута */
            'attribute_values' => $this->when(property_exists($this->resource, 'attribute_values'), function () {
                return new AttributeValueRelationResource($this->resource->attribute_values ?? []);
            }),
        ];
    }
}
