<?php

namespace App\Http\Resources\Sales\Shipments;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ShipmentItemShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор записи */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->updated_at,
            /** @var string $shipment_id Идентификатор отгрузки */
            'shipment_id' => $this->shipment_id,
            /** @var string $product_id Идентификатор товара */
            'product_id' => $this->product_id,
            /** @var int $quantity Количество товара */
            'quantity' => (int) $this->quantity,
            /** @var string $price Цена товара */
            'price' => (string) $this->price,
            /** @var string $cost Себестоимость товара */
            'cost' => (string) $this->cost,
            /** @var string $total_cost Себестоимость позиции */
            'total_cost' => (string) $this->total_cost,
            /** @var string $total_price Сумма позиции */
            'total_price' => (string) $this->total_price,
            /** @var string $profit Прибыль позиции */
            'profit' => (string) $this->profit,
            /** @var string|null $vat_rate_id Идентификатор ставки НДС */
            'vat_rate_id' => $this->vat_rate_id,
            /** @var int $discount Размер скидки */
            'discount' => (int) $this->discount,
            /** @var int $vat_rate Ставка НДС */
            'vat_rate' => $this->vat_rate,
            /** @var array{title: string, weight: string, volume: string} $product Информация о товаре */
            'product' => [
                'title' => $this->product_title,
                'weight' => (string) $this->product_weight,
                'volume' => (string) $this->product_volume,
            ],
        ];
    }
}
