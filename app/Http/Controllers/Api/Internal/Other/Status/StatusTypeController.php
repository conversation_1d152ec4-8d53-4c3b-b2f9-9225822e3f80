<?php

namespace App\Http\Controllers\Api\Internal\Other\Status;

use App\Contracts\Services\Internal\StatusTypesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Resources\Other\Status\StatusTypeResource;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class StatusTypeController extends Controller
{
    public function __construct(
        private readonly StatusTypesServiceContract $service
    ) {
    }

    /**
     * @response StatusTypeResource[]
     */
    public function index(): JsonResponse
    {
        $data = $this->service->get();
        return response()->json(StatusTypeResource::collection($data), Response::HTTP_OK);
    }
}
