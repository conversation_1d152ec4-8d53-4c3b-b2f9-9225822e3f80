<?php

namespace App\Repositories\Other\Statuses;

use App\Contracts\Repositories\StatusTypesRepositoryContract;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class StatusTypesRepository implements StatusTypesRepositoryContract
{
    protected const TABLE = 'status_types';

    public function get(): Collection
    {
        return DB::table(self::TABLE)
            ->get();
    }

    public function findFirst(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }
}
